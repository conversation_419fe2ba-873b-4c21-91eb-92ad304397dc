[{"/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/error.tsx": "1", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/for-schools/page.tsx": "2", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/kayak-rafting/page.tsx": "3", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/kayaking/page.tsx": "4", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/layout.tsx": "5", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/not-found.tsx": "6", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/page.tsx": "7", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/rafting/page.tsx": "8", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/riding/page.tsx": "9", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/river-village/page.tsx": "10", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/test-footer/page.tsx": "11", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/trekking/page.tsx": "12", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/api/csp-violations/route.ts": "13", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/global-error.tsx": "14", "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/sitemap.ts": "15", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ActivityPageLayout.tsx": "16", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/BokunWidget.tsx": "17", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ClientLayout.tsx": "18", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicBokunWidget.tsx": "19", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicContactDetails.tsx": "20", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicGallery.tsx": "21", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicGoogleMap.tsx": "22", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/EnhancedPackageCard.tsx": "23", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/Footer.tsx": "24", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/HomePageContent.tsx": "25", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/PageLayout.tsx": "26", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/SummerProgramCard.tsx": "27", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/activities-menu.tsx": "28", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/BokunStyles.tsx": "29", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/BookingButton.tsx": "30", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GDPRBanner.tsx": "31", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GDPRGoogleAnalytics.tsx": "32", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GoogleReviews.tsx": "33", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/PriceListButton.tsx": "34", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/contact-details-enhanced.tsx": "35", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/desktop-menu.tsx": "36", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/hamburger-menu.tsx": "37", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/hero/HeroSection.tsx": "38", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/language-selector.tsx": "39", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/responsive-navigation.tsx": "40", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/site-header.tsx": "41", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/standalone-menu-items.tsx": "42", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/theme-provider.tsx": "43", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/Container.tsx": "44", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/Gallery.tsx": "45", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/OptimizedImage.tsx": "46", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/PriceListPopup.tsx": "47", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/accordion.tsx": "48", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/alert-dialog.tsx": "49", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/alert.tsx": "50", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/aspect-ratio.tsx": "51", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/avatar.tsx": "52", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/badge.tsx": "53", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/border-beam.tsx": "54", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/breadcrumb.tsx": "55", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/button.tsx": "56", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/calendar.tsx": "57", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/card.tsx": "58", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/carousel.tsx": "59", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/chart.tsx": "60", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/checkbox.tsx": "61", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/collapsible.tsx": "62", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/command.tsx": "63", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/context-menu.tsx": "64", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/dialog.tsx": "65", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/drawer.tsx": "66", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/dropdown-menu.tsx": "67", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/form.tsx": "68", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/grid-pattern.tsx": "69", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/hover-card.tsx": "70", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/input-otp.tsx": "71", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/input.tsx": "72", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/label.tsx": "73", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/menubar.tsx": "74", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/navigation-menu.tsx": "75", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/number-ticker.tsx": "76", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/pagination.tsx": "77", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/popover.tsx": "78", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/progress.tsx": "79", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/pulsating-button.tsx": "80", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/radio-group.tsx": "81", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/resizable.tsx": "82", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/script-loader.tsx": "83", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/scroll-area.tsx": "84", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/select.tsx": "85", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/separator.tsx": "86", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sheet.tsx": "87", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sidebar.tsx": "88", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/skeleton.tsx": "89", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/slider.tsx": "90", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sonner.tsx": "91", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/switch.tsx": "92", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/table.tsx": "93", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/tabs.tsx": "94", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/textarea.tsx": "95", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toast.tsx": "96", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toaster.tsx": "97", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toggle-group.tsx": "98", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toggle.tsx": "99", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/tooltip.tsx": "100", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/use-mobile.tsx": "101", "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/use-toast.ts": "102", "/Users/<USER>/Documents/GitHub/ponyclub-v0/contexts/gdpr-context.tsx": "103", "/Users/<USER>/Documents/GitHub/ponyclub-v0/contexts/language-context.tsx": "104", "/Users/<USER>/Documents/GitHub/ponyclub-v0/hooks/use-mobile.tsx": "105", "/Users/<USER>/Documents/GitHub/ponyclub-v0/hooks/use-toast.ts": "106", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/bokun-lang.ts": "107", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/index.ts": "108", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/kayaking.ts": "109", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/rafting.ts": "110", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/riding.ts": "111", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/river-village.ts": "112", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/trekking.ts": "113", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/image-optimization.ts": "114", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/sitemap-data.ts": "115", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/el.ts": "116", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/en.ts": "117", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/index.ts": "118", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/use-bokun-language.ts": "119", "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/utils.ts": "120", "/Users/<USER>/Documents/GitHub/ponyclub-v0/middleware.ts": "121", "/Users/<USER>/Documents/GitHub/ponyclub-v0/next-env.d.ts": "122", "/Users/<USER>/Documents/GitHub/ponyclub-v0/scripts/generate-sitemap-data.js": "123", "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/bokun.d.ts": "124", "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/esbuild.d.ts": "125", "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/global.d.ts": "126", "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/pg-protocol.d.ts": "127", "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/vite.d.ts": "128"}, {"size": 2237, "mtime": 1749655665568, "results": "129", "hashOfConfig": "130"}, {"size": 31444, "mtime": 1749655665568, "results": "131", "hashOfConfig": "130"}, {"size": 811, "mtime": 1749582033644, "results": "132", "hashOfConfig": "133"}, {"size": 5018, "mtime": 1749655665569, "results": "134", "hashOfConfig": "130"}, {"size": 8146, "mtime": 1749655665569, "results": "135", "hashOfConfig": "130"}, {"size": 1750, "mtime": 1749655665569, "results": "136", "hashOfConfig": "130"}, {"size": 426, "mtime": 1749594729006, "results": "137", "hashOfConfig": "133"}, {"size": 4607, "mtime": 1749655665570, "results": "138", "hashOfConfig": "130"}, {"size": 2035, "mtime": 1749594741410, "results": "139", "hashOfConfig": "133"}, {"size": 4702, "mtime": 1749655665571, "results": "140", "hashOfConfig": "130"}, {"size": 1500, "mtime": 1749655665571, "results": "141", "hashOfConfig": "130"}, {"size": 16316, "mtime": 1749655665572, "results": "142", "hashOfConfig": "130"}, {"size": 765, "mtime": 1749594857483, "results": "143", "hashOfConfig": "133"}, {"size": 658, "mtime": 1749582033744, "results": "144", "hashOfConfig": "133"}, {"size": 929, "mtime": 1749594754901, "results": "145", "hashOfConfig": "133"}, {"size": 4851, "mtime": 1749655665572, "results": "146", "hashOfConfig": "130"}, {"size": 8256, "mtime": 1749594998111, "results": "147", "hashOfConfig": "133"}, {"size": 1475, "mtime": 1749582033832, "results": "148", "hashOfConfig": "133"}, {"size": 1890, "mtime": 1749655665573, "results": "149", "hashOfConfig": "130"}, {"size": 426, "mtime": 1749655665573, "results": "150", "hashOfConfig": "130"}, {"size": 632, "mtime": 1749655665574, "results": "151", "hashOfConfig": "130"}, {"size": 660, "mtime": 1749655665575, "results": "152", "hashOfConfig": "130"}, {"size": 8580, "mtime": 1749655665575, "results": "153", "hashOfConfig": "130"}, {"size": 3163, "mtime": 1749655665575, "results": "154", "hashOfConfig": "130"}, {"size": 11722, "mtime": 1749655665576, "results": "155", "hashOfConfig": "130"}, {"size": 920, "mtime": 1749655104662, "results": "156", "hashOfConfig": "133"}, {"size": 6661, "mtime": 1749655665576, "results": "157", "hashOfConfig": "130"}, {"size": 3499, "mtime": 1749655665577, "results": "158", "hashOfConfig": "130"}, {"size": 2511, "mtime": 1749582033802, "results": "159", "hashOfConfig": "133"}, {"size": 5377, "mtime": 1749655665577, "results": "160", "hashOfConfig": "130"}, {"size": 8357, "mtime": 1749655665578, "results": "161", "hashOfConfig": "130"}, {"size": 2228, "mtime": 1749582033823, "results": "162", "hashOfConfig": "133"}, {"size": 1646, "mtime": 1749655665578, "results": "163", "hashOfConfig": "130"}, {"size": 749, "mtime": 1749655665578, "results": "164", "hashOfConfig": "130"}, {"size": 4232, "mtime": 1749655665578, "results": "165", "hashOfConfig": "130"}, {"size": 8436, "mtime": 1749655665579, "results": "166", "hashOfConfig": "130"}, {"size": 6988, "mtime": 1749655665579, "results": "167", "hashOfConfig": "130"}, {"size": 3921, "mtime": 1749655665579, "results": "168", "hashOfConfig": "130"}, {"size": 2914, "mtime": 1749655665579, "results": "169", "hashOfConfig": "130"}, {"size": 1450, "mtime": 1749582033930, "results": "170", "hashOfConfig": "133"}, {"size": 1027, "mtime": 1749655665580, "results": "171", "hashOfConfig": "130"}, {"size": 1021, "mtime": 1749655665580, "results": "172", "hashOfConfig": "130"}, {"size": 287, "mtime": 1749582033941, "results": "173", "hashOfConfig": "133"}, {"size": 1071, "mtime": 1749655665580, "results": "174", "hashOfConfig": "130"}, {"size": 3351, "mtime": 1749655665581, "results": "175", "hashOfConfig": "130"}, {"size": 4059, "mtime": 1749655665581, "results": "176", "hashOfConfig": "130"}, {"size": 4148, "mtime": 1749655665581, "results": "177", "hashOfConfig": "130"}, {"size": 1977, "mtime": 1749655665582, "results": "178", "hashOfConfig": "130"}, {"size": 4302, "mtime": 1749655665582, "results": "179", "hashOfConfig": "130"}, {"size": 1532, "mtime": 1749655665582, "results": "180", "hashOfConfig": "130"}, {"size": 154, "mtime": 1749582033957, "results": "181", "hashOfConfig": "133"}, {"size": 2294, "mtime": 1749655665583, "results": "182", "hashOfConfig": "130"}, {"size": 1084, "mtime": 1749655665583, "results": "183", "hashOfConfig": "130"}, {"size": 2272, "mtime": 1749655665583, "results": "184", "hashOfConfig": "130"}, {"size": 2671, "mtime": 1749655665583, "results": "185", "hashOfConfig": "130"}, {"size": 1829, "mtime": 1749655665584, "results": "186", "hashOfConfig": "130"}, {"size": 2923, "mtime": 1749655665584, "results": "187", "hashOfConfig": "130"}, {"size": 1736, "mtime": 1749655665584, "results": "188", "hashOfConfig": "130"}, {"size": 6179, "mtime": 1749655665585, "results": "189", "hashOfConfig": "130"}, {"size": 10283, "mtime": 1749655665585, "results": "190", "hashOfConfig": "130"}, {"size": 1061, "mtime": 1749655665585, "results": "191", "hashOfConfig": "130"}, {"size": 329, "mtime": 1749582034004, "results": "192", "hashOfConfig": "133"}, {"size": 4815, "mtime": 1749655665585, "results": "193", "hashOfConfig": "130"}, {"size": 7162, "mtime": 1749655665586, "results": "194", "hashOfConfig": "130"}, {"size": 3775, "mtime": 1749655665586, "results": "195", "hashOfConfig": "130"}, {"size": 2933, "mtime": 1749655665586, "results": "196", "hashOfConfig": "130"}, {"size": 7355, "mtime": 1749655665587, "results": "197", "hashOfConfig": "130"}, {"size": 4021, "mtime": 1749655665588, "results": "198", "hashOfConfig": "130"}, {"size": 1422, "mtime": 1749655665588, "results": "199", "hashOfConfig": "130"}, {"size": 1200, "mtime": 1749655665589, "results": "200", "hashOfConfig": "130"}, {"size": 2157, "mtime": 1749655665589, "results": "201", "hashOfConfig": "130"}, {"size": 793, "mtime": 1749655665589, "results": "202", "hashOfConfig": "130"}, {"size": 702, "mtime": 1749655665590, "results": "203", "hashOfConfig": "130"}, {"size": 7832, "mtime": 1749655665591, "results": "204", "hashOfConfig": "130"}, {"size": 4994, "mtime": 1749655665592, "results": "205", "hashOfConfig": "130"}, {"size": 1702, "mtime": 1749655665592, "results": "206", "hashOfConfig": "130"}, {"size": 2705, "mtime": 1749655665592, "results": "207", "hashOfConfig": "130"}, {"size": 1246, "mtime": 1749655665593, "results": "208", "hashOfConfig": "130"}, {"size": 773, "mtime": 1749655665593, "results": "209", "hashOfConfig": "130"}, {"size": 1055, "mtime": 1749655665593, "results": "210", "hashOfConfig": "130"}, {"size": 1451, "mtime": 1749655665594, "results": "211", "hashOfConfig": "130"}, {"size": 1703, "mtime": 1749655665594, "results": "212", "hashOfConfig": "130"}, {"size": 2741, "mtime": 1749591564793, "results": "213", "hashOfConfig": "133"}, {"size": 1613, "mtime": 1749655665594, "results": "214", "hashOfConfig": "130"}, {"size": 5541, "mtime": 1749655665594, "results": "215", "hashOfConfig": "130"}, {"size": 706, "mtime": 1749655665595, "results": "216", "hashOfConfig": "133"}, {"size": 4188, "mtime": 1749655665595, "results": "217", "hashOfConfig": "130"}, {"size": 22720, "mtime": 1749655665595, "results": "218", "hashOfConfig": "130"}, {"size": 231, "mtime": 1749582034149, "results": "219", "hashOfConfig": "133"}, {"size": 1075, "mtime": 1749655665595, "results": "220", "hashOfConfig": "130"}, {"size": 870, "mtime": 1749582034152, "results": "221", "hashOfConfig": "133"}, {"size": 1155, "mtime": 1749655665596, "results": "222", "hashOfConfig": "130"}, {"size": 2666, "mtime": 1749655665596, "results": "223", "hashOfConfig": "130"}, {"size": 1901, "mtime": 1749655665596, "results": "224", "hashOfConfig": "130"}, {"size": 711, "mtime": 1749655665597, "results": "225", "hashOfConfig": "130"}, {"size": 4777, "mtime": 1749655665597, "results": "226", "hashOfConfig": "130"}, {"size": 739, "mtime": 1749582034170, "results": "227", "hashOfConfig": "133"}, {"size": 1714, "mtime": 1749655665597, "results": "228", "hashOfConfig": "130"}, {"size": 1515, "mtime": 1749655665597, "results": "229", "hashOfConfig": "130"}, {"size": 1159, "mtime": 1749655665598, "results": "230", "hashOfConfig": "130"}, {"size": 565, "mtime": 1749582034181, "results": "231", "hashOfConfig": "133"}, {"size": 3901, "mtime": 1749582034187, "results": "232", "hashOfConfig": "133"}, {"size": 4440, "mtime": 1749582034197, "results": "233", "hashOfConfig": "133"}, {"size": 2547, "mtime": 1749655104662, "results": "234", "hashOfConfig": "133"}, {"size": 565, "mtime": 1749582034336, "results": "235", "hashOfConfig": "133"}, {"size": 3901, "mtime": 1749655104662, "results": "236", "hashOfConfig": "133"}, {"size": 104, "mtime": 1749582034344, "results": "237", "hashOfConfig": "133"}, {"size": 105, "mtime": 1749582034345, "results": "238", "hashOfConfig": "133"}, {"size": 1174, "mtime": 1749582034346, "results": "239", "hashOfConfig": "133"}, {"size": 1488, "mtime": 1749582034347, "results": "240", "hashOfConfig": "133"}, {"size": 1054, "mtime": 1749582034349, "results": "241", "hashOfConfig": "133"}, {"size": 1449, "mtime": 1749582034350, "results": "242", "hashOfConfig": "133"}, {"size": 1245, "mtime": 1749582034351, "results": "243", "hashOfConfig": "133"}, {"size": 2975, "mtime": 1749582034354, "results": "244", "hashOfConfig": "133"}, {"size": 688, "mtime": 1749655106309, "results": "245", "hashOfConfig": "133"}, {"size": 15799, "mtime": 1749582034361, "results": "246", "hashOfConfig": "133"}, {"size": 9673, "mtime": 1749582034365, "results": "247", "hashOfConfig": "133"}, {"size": 173, "mtime": 1749582034366, "results": "248", "hashOfConfig": "133"}, {"size": 992, "mtime": 1749582034368, "results": "249", "hashOfConfig": "133"}, {"size": 166, "mtime": 1749582034369, "results": "250", "hashOfConfig": "133"}, {"size": 8817, "mtime": 1749600042190, "results": "251", "hashOfConfig": "133"}, {"size": 211, "mtime": 1749206852000, "results": "252", "hashOfConfig": "253"}, {"size": 2569, "mtime": 1749582034444, "results": "254", "hashOfConfig": "255"}, {"size": 264, "mtime": 1749582034460, "results": "256", "hashOfConfig": "253"}, {"size": 298, "mtime": 1749582034461, "results": "257", "hashOfConfig": "253"}, {"size": 1352, "mtime": 1749644215638, "results": "258", "hashOfConfig": "253"}, {"size": 152, "mtime": 1749582034466, "results": "259", "hashOfConfig": "253"}, {"size": 163, "mtime": 1749582034467, "results": "260", "hashOfConfig": "253"}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "qph1v", {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "117nvzu", {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 30, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "zemn09", {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1j4h9xt", {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/error.tsx", ["645"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/for-schools/page.tsx", ["646", "647"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/kayak-rafting/page.tsx", ["648"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/kayaking/page.tsx", ["649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/layout.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/not-found.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/page.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/rafting/page.tsx", ["661", "662", "663", "664", "665", "666", "667", "668", "669"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/riding/page.tsx", ["670"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/river-village/page.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/test-footer/page.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/[locale]/trekking/page.tsx", ["671", "672", "673", "674", "675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/api/csp-violations/route.ts", ["701", "702"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/global-error.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/app/sitemap.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ActivityPageLayout.tsx", ["703"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/BokunWidget.tsx", ["704", "705", "706", "707", "708", "709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ClientLayout.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicBokunWidget.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicContactDetails.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicGallery.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/DynamicGoogleMap.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/EnhancedPackageCard.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/Footer.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/HomePageContent.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/PageLayout.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/SummerProgramCard.tsx", ["724", "725"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/activities-menu.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/BokunStyles.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/BookingButton.tsx", ["726", "727"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GDPRBanner.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GDPRGoogleAnalytics.tsx", ["728", "729", "730", "731", "732"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/GoogleReviews.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/client/PriceListButton.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/contact-details-enhanced.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/desktop-menu.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/hamburger-menu.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/hero/HeroSection.tsx", ["733"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/language-selector.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/responsive-navigation.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/site-header.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/standalone-menu-items.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/theme-provider.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/Container.tsx", ["734"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/Gallery.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/OptimizedImage.tsx", ["735", "736"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/PriceListPopup.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/accordion.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/alert-dialog.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/alert.tsx", ["737"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/aspect-ratio.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/avatar.tsx", ["738", "739"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/badge.tsx", ["740"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/border-beam.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/breadcrumb.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/button.tsx", ["741"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/calendar.tsx", ["742"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/card.tsx", ["743"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/carousel.tsx", ["744"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/chart.tsx", ["745", "746", "747", "748", "749", "750"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/checkbox.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/collapsible.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/command.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/context-menu.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/dialog.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/drawer.tsx", ["751"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/dropdown-menu.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/form.tsx", ["752", "753", "754", "755"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/grid-pattern.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/hover-card.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/input-otp.tsx", ["756"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/input.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/label.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/menubar.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/navigation-menu.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/number-ticker.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/pagination.tsx", ["757"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/popover.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/progress.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/pulsating-button.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/radio-group.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/resizable.tsx", ["758"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/script-loader.tsx", ["759"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/scroll-area.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/select.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/separator.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sheet.tsx", ["760", "761"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sidebar.tsx", ["762", "763", "764", "765"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/skeleton.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/slider.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/sonner.tsx", ["766"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/switch.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/table.tsx", ["767", "768", "769", "770", "771", "772"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/tabs.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/textarea.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toast.tsx", ["773"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toaster.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toggle-group.tsx", ["774", "775"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/toggle.tsx", ["776"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/tooltip.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/use-mobile.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/components/ui/use-toast.ts", ["777"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/contexts/gdpr-context.tsx", ["778", "779"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/contexts/language-context.tsx", ["780"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/hooks/use-mobile.tsx", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/hooks/use-toast.ts", ["781"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/bokun-lang.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/index.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/kayaking.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/rafting.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/riding.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/river-village.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/gallery-data/trekking.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/image-optimization.ts", ["782"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/sitemap-data.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/el.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/en.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/translations/index.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/use-bokun-language.ts", ["783"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/lib/utils.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/middleware.ts", ["784"], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/next-env.d.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/scripts/generate-sitemap-data.js", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/bokun.d.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/esbuild.d.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/global.d.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/pg-protocol.d.ts", [], [], "/Users/<USER>/Documents/GitHub/ponyclub-v0/types/vite.d.ts", [], [], {"ruleId": "785", "severity": 1, "message": "786", "line": 21, "column": 7, "nodeType": "787", "messageId": "788", "endLine": 21, "endColumn": 20, "suggestions": "789"}, {"ruleId": "790", "severity": 1, "message": "791", "line": 396, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 396, "endColumn": 119}, {"ruleId": "790", "severity": 1, "message": "794", "line": 499, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 499, "endColumn": 121}, {"ruleId": "790", "severity": 1, "message": "795", "line": 14, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 14, "endColumn": 109}, {"ruleId": "790", "severity": 1, "message": "794", "line": 41, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 41, "endColumn": 121}, {"ruleId": "790", "severity": 1, "message": "791", "line": 42, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 42, "endColumn": 119}, {"ruleId": "790", "severity": 1, "message": "796", "line": 47, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 47, "endColumn": 112}, {"ruleId": "790", "severity": 1, "message": "797", "line": 48, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 48, "endColumn": 111}, {"ruleId": "790", "severity": 1, "message": "798", "line": 53, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 53, "endColumn": 115}, {"ruleId": "790", "severity": 1, "message": "799", "line": 54, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 54, "endColumn": 116}, {"ruleId": "790", "severity": 1, "message": "794", "line": 64, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 64, "endColumn": 121}, {"ruleId": "790", "severity": 1, "message": "800", "line": 65, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 65, "endColumn": 114}, {"ruleId": "790", "severity": 1, "message": "801", "line": 69, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 69, "endColumn": 120}, {"ruleId": "790", "severity": 1, "message": "802", "line": 70, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 70, "endColumn": 118}, {"ruleId": "790", "severity": 1, "message": "794", "line": 75, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 75, "endColumn": 121}, {"ruleId": "790", "severity": 1, "message": "800", "line": 76, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 76, "endColumn": 114}, {"ruleId": "790", "severity": 1, "message": "802", "line": 41, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 41, "endColumn": 118}, {"ruleId": "790", "severity": 1, "message": "803", "line": 46, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 46, "endColumn": 117}, {"ruleId": "790", "severity": 1, "message": "802", "line": 47, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 47, "endColumn": 118}, {"ruleId": "790", "severity": 1, "message": "794", "line": 52, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 52, "endColumn": 121}, {"ruleId": "790", "severity": 1, "message": "804", "line": 53, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 53, "endColumn": 107}, {"ruleId": "790", "severity": 1, "message": "801", "line": 62, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 62, "endColumn": 120}, {"ruleId": "790", "severity": 1, "message": "803", "line": 67, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 67, "endColumn": 117}, {"ruleId": "790", "severity": 1, "message": "804", "line": 68, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 68, "endColumn": 107}, {"ruleId": "790", "severity": 1, "message": "791", "line": 72, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 72, "endColumn": 119}, {"ruleId": "790", "severity": 1, "message": "795", "line": 37, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 37, "endColumn": 109}, {"ruleId": "790", "severity": 1, "message": "791", "line": 40, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 40, "endColumn": 119}, {"ruleId": "790", "severity": 1, "message": "796", "line": 41, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 41, "endColumn": 112}, {"ruleId": "790", "severity": 1, "message": "805", "line": 42, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 42, "endColumn": 110}, {"ruleId": "790", "severity": 1, "message": "801", "line": 43, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 43, "endColumn": 120}, {"ruleId": "790", "severity": 1, "message": "798", "line": 47, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 47, "endColumn": 115}, {"ruleId": "790", "severity": 1, "message": "791", "line": 64, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 64, "endColumn": 119}, {"ruleId": "790", "severity": 1, "message": "798", "line": 65, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 65, "endColumn": 115}, {"ruleId": "790", "severity": 1, "message": "801", "line": 66, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 66, "endColumn": 120}, {"ruleId": "790", "severity": 1, "message": "798", "line": 84, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 84, "endColumn": 115}, {"ruleId": "790", "severity": 1, "message": "794", "line": 85, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 85, "endColumn": 121}, {"ruleId": "790", "severity": 1, "message": "801", "line": 86, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 86, "endColumn": 120}, {"ruleId": "790", "severity": 1, "message": "798", "line": 104, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 104, "endColumn": 115}, {"ruleId": "790", "severity": 1, "message": "802", "line": 105, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 105, "endColumn": 118}, {"ruleId": "790", "severity": 1, "message": "794", "line": 106, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 106, "endColumn": 121}, {"ruleId": "790", "severity": 1, "message": "802", "line": 117, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 117, "endColumn": 118}, {"ruleId": "790", "severity": 1, "message": "801", "line": 118, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 118, "endColumn": 120}, {"ruleId": "790", "severity": 1, "message": "802", "line": 119, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 119, "endColumn": 118}, {"ruleId": "790", "severity": 1, "message": "801", "line": 120, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 120, "endColumn": 120}, {"ruleId": "790", "severity": 1, "message": "803", "line": 123, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 123, "endColumn": 117}, {"ruleId": "790", "severity": 1, "message": "791", "line": 138, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 138, "endColumn": 119}, {"ruleId": "790", "severity": 1, "message": "794", "line": 139, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 139, "endColumn": 121}, {"ruleId": "790", "severity": 1, "message": "801", "line": 140, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 140, "endColumn": 120}, {"ruleId": "790", "severity": 1, "message": "803", "line": 158, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 158, "endColumn": 117}, {"ruleId": "790", "severity": 1, "message": "799", "line": 159, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 159, "endColumn": 116}, {"ruleId": "790", "severity": 1, "message": "806", "line": 160, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 160, "endColumn": 106}, {"ruleId": "790", "severity": 1, "message": "801", "line": 177, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 177, "endColumn": 120}, {"ruleId": "790", "severity": 1, "message": "791", "line": 178, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 178, "endColumn": 119}, {"ruleId": "790", "severity": 1, "message": "797", "line": 179, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 179, "endColumn": 111}, {"ruleId": "790", "severity": 1, "message": "802", "line": 196, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 196, "endColumn": 118}, {"ruleId": "790", "severity": 1, "message": "807", "line": 202, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 202, "endColumn": 103}, {"ruleId": "785", "severity": 1, "message": "786", "line": 8, "column": 7, "nodeType": "787", "messageId": "788", "endLine": 8, "endColumn": 19, "suggestions": "808"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 16, "column": 7, "nodeType": "787", "messageId": "788", "endLine": 16, "endColumn": 20, "suggestions": "809"}, {"ruleId": "790", "severity": 1, "message": "810", "line": 51, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 51, "endColumn": 113}, {"ruleId": "785", "severity": 1, "message": "786", "line": 40, "column": 9, "nodeType": "787", "messageId": "788", "endLine": 40, "endColumn": 20, "suggestions": "811"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 46, "column": 7, "nodeType": "787", "messageId": "788", "endLine": 46, "endColumn": 18, "suggestions": "812"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 59, "column": 11, "nodeType": "787", "messageId": "788", "endLine": 59, "endColumn": 22, "suggestions": "813"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 65, "column": 11, "nodeType": "787", "messageId": "788", "endLine": 65, "endColumn": 22, "suggestions": "814"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 70, "column": 11, "nodeType": "787", "messageId": "788", "endLine": 70, "endColumn": 22, "suggestions": "815"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 92, "column": 9, "nodeType": "787", "messageId": "788", "endLine": 92, "endColumn": 20, "suggestions": "816"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 112, "column": 11, "nodeType": "787", "messageId": "788", "endLine": 112, "endColumn": 22, "suggestions": "817"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 118, "column": 13, "nodeType": "787", "messageId": "788", "endLine": 118, "endColumn": 24, "suggestions": "818"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 122, "column": 13, "nodeType": "787", "messageId": "788", "endLine": 122, "endColumn": 26, "suggestions": "819"}, {"ruleId": "790", "severity": 1, "message": "804", "line": 125, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 125, "endColumn": 107}, {"ruleId": "785", "severity": 1, "message": "786", "line": 128, "column": 11, "nodeType": "787", "messageId": "788", "endLine": 128, "endColumn": 22, "suggestions": "820"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 132, "column": 9, "nodeType": "787", "messageId": "788", "endLine": 132, "endColumn": 20, "suggestions": "821"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 137, "column": 7, "nodeType": "787", "messageId": "788", "endLine": 137, "endColumn": 18, "suggestions": "822"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 147, "column": 11, "nodeType": "787", "messageId": "788", "endLine": 147, "endColumn": 22, "suggestions": "823"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 153, "column": 13, "nodeType": "787", "messageId": "788", "endLine": 153, "endColumn": 24, "suggestions": "824"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 159, "column": 13, "nodeType": "787", "messageId": "788", "endLine": 159, "endColumn": 26, "suggestions": "825"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 167, "column": 7, "nodeType": "787", "messageId": "788", "endLine": 167, "endColumn": 18, "suggestions": "826"}, {"ruleId": "790", "severity": 1, "message": "796", "line": 170, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 170, "endColumn": 112}, {"ruleId": "785", "severity": 1, "message": "786", "line": 175, "column": 9, "nodeType": "787", "messageId": "788", "endLine": 175, "endColumn": 20, "suggestions": "827"}, {"ruleId": "828", "severity": 1, "message": "829", "line": 189, "column": 20, "nodeType": null, "endLine": 189, "endColumn": 31}, {"ruleId": "790", "severity": 1, "message": "806", "line": 84, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 84, "endColumn": 106}, {"ruleId": "790", "severity": 1, "message": "830", "line": 90, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 90, "endColumn": 159}, {"ruleId": "785", "severity": 1, "message": "786", "line": 104, "column": 9, "nodeType": "787", "messageId": "788", "endLine": 104, "endColumn": 21, "suggestions": "831"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 137, "column": 7, "nodeType": "787", "messageId": "788", "endLine": 137, "endColumn": 18, "suggestions": "832"}, {"ruleId": "833", "severity": 1, "message": "834", "line": 6, "column": 19, "nodeType": null, "messageId": "835", "endLine": 6, "endColumn": 32}, {"ruleId": "785", "severity": 1, "message": "786", "line": 24, "column": 7, "nodeType": "787", "messageId": "788", "endLine": 24, "endColumn": 18, "suggestions": "836"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 53, "column": 7, "nodeType": "787", "messageId": "788", "endLine": 53, "endColumn": 18, "suggestions": "837"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 65, "column": 11, "nodeType": "787", "messageId": "788", "endLine": 65, "endColumn": 22, "suggestions": "838"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 68, "column": 11, "nodeType": "787", "messageId": "788", "endLine": 68, "endColumn": 24, "suggestions": "839"}, {"ruleId": "833", "severity": 1, "message": "840", "line": 4, "column": 29, "nodeType": null, "messageId": "835", "endLine": 4, "endColumn": 37}, {"ruleId": "833", "severity": 1, "message": "841", "line": 2, "column": 17, "nodeType": null, "messageId": "835", "endLine": 2, "endColumn": 26}, {"ruleId": "842", "severity": 1, "message": "843", "line": 22, "column": 8, "nodeType": "844", "messageId": "845", "endLine": 107, "endColumn": 2}, {"ruleId": "846", "severity": 1, "message": "847", "line": 38, "column": 66, "nodeType": "848", "messageId": "849", "endLine": 38, "endColumn": 69, "suggestions": "850"}, {"ruleId": "790", "severity": 1, "message": "795", "line": 36, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 36, "endColumn": 109}, {"ruleId": "790", "severity": 1, "message": "851", "line": 28, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 28, "endColumn": 147}, {"ruleId": "790", "severity": 1, "message": "810", "line": 40, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 40, "endColumn": 113}, {"ruleId": "790", "severity": 1, "message": "796", "line": 23, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 23, "endColumn": 112}, {"ruleId": "790", "severity": 1, "message": "852", "line": 42, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 42, "endColumn": 102}, {"ruleId": "833", "severity": 1, "message": "853", "line": 6, "column": 26, "nodeType": null, "messageId": "835", "endLine": 6, "endColumn": 42}, {"ruleId": "790", "severity": 1, "message": "801", "line": 5, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 5, "endColumn": 120}, {"ruleId": "790", "severity": 1, "message": "854", "line": 43, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 43, "endColumn": 105}, {"ruleId": "833", "severity": 1, "message": "855", "line": 5, "column": 10, "nodeType": null, "messageId": "835", "endLine": 5, "endColumn": 18}, {"ruleId": "833", "severity": 1, "message": "856", "line": 5, "column": 20, "nodeType": null, "messageId": "835", "endLine": 5, "endColumn": 27}, {"ruleId": "833", "severity": 1, "message": "857", "line": 5, "column": 29, "nodeType": null, "messageId": "835", "endLine": 5, "endColumn": 38}, {"ruleId": "790", "severity": 1, "message": "806", "line": 16, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 16, "endColumn": 106}, {"ruleId": "842", "severity": 1, "message": "858", "line": 175, "column": 24, "nodeType": "859", "messageId": "845", "endLine": 232, "endColumn": 12}, {"ruleId": "790", "severity": 1, "message": "806", "line": 315, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 315, "endColumn": 106}, {"ruleId": "790", "severity": 1, "message": "799", "line": 8, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 8, "endColumn": 116}, {"ruleId": "790", "severity": 1, "message": "800", "line": 88, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 88, "endColumn": 114}, {"ruleId": "790", "severity": 1, "message": "860", "line": 105, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 105, "endColumn": 108}, {"ruleId": "790", "severity": 1, "message": "861", "line": 114, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 114, "endColumn": 104}, {"ruleId": "790", "severity": 1, "message": "854", "line": 132, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 132, "endColumn": 105}, {"ruleId": "790", "severity": 1, "message": "791", "line": 9, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 9, "endColumn": 119}, {"ruleId": "790", "severity": 1, "message": "807", "line": 50, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 50, "endColumn": 103}, {"ruleId": "790", "severity": 1, "message": "791", "line": 8, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 8, "endColumn": 119}, {"ruleId": "833", "severity": 1, "message": "862", "line": 34, "column": 6, "nodeType": null, "messageId": "835", "endLine": 34, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "804", "line": 56, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 56, "endColumn": 107}, {"ruleId": "790", "severity": 1, "message": "807", "line": 60, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 60, "endColumn": 103}, {"ruleId": "790", "severity": 1, "message": "802", "line": 53, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 53, "endColumn": 118}, {"ruleId": "790", "severity": 1, "message": "797", "line": 222, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 222, "endColumn": 111}, {"ruleId": "790", "severity": 1, "message": "804", "line": 290, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 290, "endColumn": 107}, {"ruleId": "790", "severity": 1, "message": "791", "line": 317, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 317, "endColumn": 119}, {"ruleId": "828", "severity": 1, "message": "863", "line": 14, "column": 18, "nodeType": null, "endLine": 14, "endColumn": 25}, {"ruleId": "790", "severity": 1, "message": "805", "line": 14, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 14, "endColumn": 110}, {"ruleId": "790", "severity": 1, "message": "860", "line": 19, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 19, "endColumn": 108}, {"ruleId": "790", "severity": 1, "message": "805", "line": 26, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 26, "endColumn": 110}, {"ruleId": "790", "severity": 1, "message": "861", "line": 44, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 44, "endColumn": 104}, {"ruleId": "790", "severity": 1, "message": "861", "line": 58, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 58, "endColumn": 104}, {"ruleId": "790", "severity": 1, "message": "797", "line": 65, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 65, "endColumn": 111}, {"ruleId": "790", "severity": 1, "message": "795", "line": 46, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 46, "endColumn": 109}, {"ruleId": "790", "severity": 1, "message": "854", "line": 17, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 17, "endColumn": 105}, {"ruleId": "790", "severity": 1, "message": "854", "line": 28, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 28, "endColumn": 105}, {"ruleId": "790", "severity": 1, "message": "795", "line": 34, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 34, "endColumn": 109}, {"ruleId": "833", "severity": 1, "message": "864", "line": 18, "column": 7, "nodeType": null, "messageId": "865", "endLine": 18, "endColumn": 18}, {"ruleId": "785", "severity": 1, "message": "786", "line": 49, "column": 9, "nodeType": "787", "messageId": "788", "endLine": 49, "endColumn": 22, "suggestions": "866"}, {"ruleId": "785", "severity": 1, "message": "786", "line": 157, "column": 3, "nodeType": "787", "messageId": "788", "endLine": 157, "endColumn": 14, "suggestions": "867"}, {"ruleId": "790", "severity": 1, "message": "805", "line": 64, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 64, "endColumn": 110}, {"ruleId": "833", "severity": 1, "message": "864", "line": 18, "column": 7, "nodeType": null, "messageId": "865", "endLine": 18, "endColumn": 18}, {"ruleId": "790", "severity": 1, "message": "799", "line": 56, "column": 1, "nodeType": "792", "messageId": "793", "endLine": 56, "endColumn": 116}, {"ruleId": "846", "severity": 1, "message": "847", "line": 6, "column": 30, "nodeType": "848", "messageId": "849", "endLine": 6, "endColumn": 33, "suggestions": "868"}, {"ruleId": "833", "severity": 1, "message": "869", "line": 12, "column": 7, "nodeType": null, "messageId": "835", "endLine": 12, "endColumn": 23}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["870"], "max-len", "This line has a length of 118. Maximum allowed is 100.", "Program", "max", "This line has a length of 120. Maximum allowed is 100.", "This line has a length of 108. Maximum allowed is 100.", "This line has a length of 111. Maximum allowed is 100.", "This line has a length of 110. Maximum allowed is 100.", "This line has a length of 114. Maximum allowed is 100.", "This line has a length of 115. Maximum allowed is 100.", "This line has a length of 113. Maximum allowed is 100.", "This line has a length of 119. Maximum allowed is 100.", "This line has a length of 117. Maximum allowed is 100.", "This line has a length of 116. Maximum allowed is 100.", "This line has a length of 106. Maximum allowed is 100.", "This line has a length of 109. Maximum allowed is 100.", "This line has a length of 105. Maximum allowed is 100.", "This line has a length of 102. Maximum allowed is 100.", ["871"], ["872"], "This line has a length of 112. Maximum allowed is 100.", ["873"], ["874"], ["875"], ["876"], ["877"], ["878"], ["879"], ["880"], ["881"], ["882"], ["883"], ["884"], ["885"], ["886"], ["887"], ["888"], ["889"], "better-tailwindcss/no-unregistered-classes", "Unregistered class detected: bokunWidget", "This line has a length of 158. Maximum allowed is 100.", ["890"], ["891"], "@typescript-eslint/no-unused-vars", "'CookieConsent' is defined but never used. Allowed unused vars must match /^_/u.", "unusedVar", ["892"], ["893"], ["894"], ["895"], "'useState' is defined but never used. Allowed unused vars must match /^_/u.", "'ReactNode' is defined but never used. Allowed unused vars must match /^_/u.", "complexity", "Function 'OptimizedImage' has a complexity of 17. Maximum allowed is 15.", "FunctionDeclaration", "complex", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["896", "897"], "This line has a length of 146. Maximum allowed is 100.", "This line has a length of 101. Maximum allowed is 100.", "'CustomComponents' is defined but never used. Allowed unused vars must match /^_/u.", "This line has a length of 104. Maximum allowed is 100.", "'NameType' is defined but never used. Allowed unused vars must match /^_/u.", "'Payload' is defined but never used. Allowed unused vars must match /^_/u.", "'ValueType' is defined but never used. Allowed unused vars must match /^_/u.", "Arrow function has a complexity of 20. Maximum allowed is 15.", "ArrowFunctionExpression", "This line has a length of 107. Maximum allowed is 100.", "This line has a length of 103. Maximum allowed is 100.", "'props' is defined but never used. Allowed unused args must match /^_/u.", "Unregistered class detected: toaster", "'actionTypes' is assigned a value but only used as a type. Allowed unused vars must match /^_/u.", "usedOnlyAsType", ["898"], ["899"], ["900", "901"], "'devScriptSources' is assigned a value but never used. Allowed unused vars must match /^_/u.", {"fix": "902", "messageId": "903", "data": "904", "desc": "905"}, {"fix": "906", "messageId": "903", "data": "907", "desc": "908"}, {"fix": "909", "messageId": "903", "data": "910", "desc": "905"}, {"fix": "911", "messageId": "903", "data": "912", "desc": "913"}, {"fix": "914", "messageId": "903", "data": "915", "desc": "913"}, {"fix": "916", "messageId": "903", "data": "917", "desc": "913"}, {"fix": "918", "messageId": "903", "data": "919", "desc": "913"}, {"fix": "920", "messageId": "903", "data": "921", "desc": "913"}, {"fix": "922", "messageId": "903", "data": "923", "desc": "913"}, {"fix": "924", "messageId": "903", "data": "925", "desc": "913"}, {"fix": "926", "messageId": "903", "data": "927", "desc": "913"}, {"fix": "928", "messageId": "903", "data": "929", "desc": "905"}, {"fix": "930", "messageId": "903", "data": "931", "desc": "913"}, {"fix": "932", "messageId": "903", "data": "933", "desc": "913"}, {"fix": "934", "messageId": "903", "data": "935", "desc": "913"}, {"fix": "936", "messageId": "903", "data": "937", "desc": "913"}, {"fix": "938", "messageId": "903", "data": "939", "desc": "913"}, {"fix": "940", "messageId": "903", "data": "941", "desc": "905"}, {"fix": "942", "messageId": "903", "data": "943", "desc": "913"}, {"fix": "944", "messageId": "903", "data": "945", "desc": "913"}, {"fix": "946", "messageId": "903", "data": "947", "desc": "908"}, {"fix": "948", "messageId": "903", "data": "949", "desc": "913"}, {"fix": "950", "messageId": "903", "data": "951", "desc": "913"}, {"fix": "952", "messageId": "903", "data": "953", "desc": "913"}, {"fix": "954", "messageId": "903", "data": "955", "desc": "913"}, {"fix": "956", "messageId": "903", "data": "957", "desc": "905"}, {"messageId": "958", "fix": "959", "desc": "960"}, {"messageId": "961", "fix": "962", "desc": "963"}, {"fix": "964", "messageId": "903", "data": "965", "desc": "905"}, {"fix": "966", "messageId": "903", "data": "967", "desc": "913"}, {"messageId": "958", "fix": "968", "desc": "960"}, {"messageId": "961", "fix": "969", "desc": "963"}, {"range": "970", "text": "971"}, "removeConsole", {"propertyName": "972"}, "Remove the console.error().", {"range": "973", "text": "971"}, {"propertyName": "974"}, "Remove the console.warn().", {"range": "975", "text": "971"}, {"propertyName": "972"}, {"range": "976", "text": "971"}, {"propertyName": "977"}, "Remove the console.log().", {"range": "978", "text": "971"}, {"propertyName": "977"}, {"range": "979", "text": "971"}, {"propertyName": "977"}, {"range": "980", "text": "971"}, {"propertyName": "977"}, {"range": "981", "text": "971"}, {"propertyName": "977"}, {"range": "982", "text": "971"}, {"propertyName": "977"}, {"range": "983", "text": "971"}, {"propertyName": "977"}, {"range": "984", "text": "971"}, {"propertyName": "977"}, {"range": "985", "text": "971"}, {"propertyName": "972"}, {"range": "986", "text": "971"}, {"propertyName": "977"}, {"range": "987", "text": "971"}, {"propertyName": "977"}, {"range": "988", "text": "971"}, {"propertyName": "977"}, {"range": "989", "text": "971"}, {"propertyName": "977"}, {"range": "990", "text": "971"}, {"propertyName": "977"}, {"range": "991", "text": "971"}, {"propertyName": "972"}, {"range": "992", "text": "971"}, {"propertyName": "977"}, {"range": "993", "text": "971"}, {"propertyName": "977"}, {"range": "994", "text": "971"}, {"propertyName": "974"}, {"range": "995", "text": "971"}, {"propertyName": "977"}, {"range": "996", "text": "971"}, {"propertyName": "977"}, {"range": "997", "text": "971"}, {"propertyName": "977"}, {"range": "998", "text": "971"}, {"propertyName": "977"}, {"range": "999", "text": "971"}, {"propertyName": "972"}, "suggestUnknown", {"range": "1000", "text": "1001"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1002", "text": "1003"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1004", "text": "971"}, {"propertyName": "972"}, {"range": "1005", "text": "971"}, {"propertyName": "977"}, {"range": "1006", "text": "1001"}, {"range": "1007", "text": "1003"}, [598, 618], "", "error", [236, 274], "warn", [614, 666], [1195, 1256], "log", [1341, 1435], [1936, 2035], [2234, 2305], [2438, 2523], [3350, 3537], [4206, 4311], [4536, 4620], [4727, 4827], [5082, 5165], [5281, 5378], [5451, 5548], [6113, 6216], [6431, 6510], [6739, 6834], [6998, 7094], [7391, 7471], [3581, 3797], [4657, 4819], [885, 935], [1717, 1786], [2056, 2096], [2143, 2193], [1168, 1171], "unknown", [1168, 1171], "never", [1445, 1498], [3956, 4003], [179, 182], [179, 182]]