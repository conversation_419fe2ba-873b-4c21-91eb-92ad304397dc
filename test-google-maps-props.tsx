// Test file to verify GoogleMapsEmbed prop types
import { GoogleMapsEmbed } from '@next/third-parties/google'

// This should work - using only supported props
const ValidComponent = () => (
  <GoogleMapsEmbed
    apiKey="test-key"
    height={400}
    width="100%"
    mode="place"
    q="test-location"
    loading="lazy"
    allowfullscreen
    style="border: 0;"
  />
)

// This should cause TypeScript errors - using unsupported props
const InvalidComponent = () => (
  <GoogleMapsEmbed
    apiKey="test-key"
    height={400}
    width="100%"
    mode="place"
    q="test-location"
    loading="lazy"
    allowfullscreen
    style="border: 0;"
    aria-label="This should cause an error"
    title="This should also cause an error"
  />
)

export { ValidComponent }
