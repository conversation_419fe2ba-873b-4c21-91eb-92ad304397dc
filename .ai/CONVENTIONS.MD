# Project Conventions

This project adheres to the coding conventions, patterns, and best practices outlined in the following files:

- `/.clinerules/.rules.md`

Please refer to these documents for detailed guidelines on:

- Next.js 15
- React 19
- TypeScript
- Tailwind CSS 4.0
- Code style and structure
- Naming conventions
- Component architecture
- And other development practices.

Any project-specific deviations or additions to these rules will be documented here.
